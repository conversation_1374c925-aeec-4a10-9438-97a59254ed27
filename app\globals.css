@import 'tailwindcss';

:root {
	--background: #ffffff;
	--foreground: #171717;
}

@theme inline {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--font-sans: var(--font-geist-sans);
	--font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
	:root {
		--background: #0a0a0a;
		--foreground: #ededed;
	}
}

body {
	background: var(--background);
	color: var(--foreground);
	font-family: Arial, Helvetica, sans-serif;
}

/* Stiluri personalizate pentru React Flow */
.react-flow__node-itineraryNode {
	background: transparent;
	border: none;
}

.react-flow__handle {
	border: 2px solid #374151;
	background: #f9fafb;
	width: 8px;
	height: 8px;
}

.react-flow__handle.react-flow__handle-top,
.react-flow__handle.react-flow__handle-bottom {
	left: 50%;
	transform: translateX(-50%);
}

.react-flow__handle.react-flow__handle-left,
.react-flow__handle.react-flow__handle-right {
	top: 50%;
	transform: translateY(-50%);
}

.react-flow__edge-path {
	stroke: #6b7280;
	stroke-width: 2;
}

.react-flow__edge.selected .react-flow__edge-path {
	stroke: #3b82f6;
	stroke-width: 3;
}
