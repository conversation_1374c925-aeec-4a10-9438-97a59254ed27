import ItineraryCanvas from '~/components/itinerary-canvas';
import { ModeToggle } from '~/components/theme/theme-toggle-button';

export default function Home() {
	return (
		<div className="bg-background h-screen w-full">
			<header className="border-border bg-background flex items-center justify-between border-b px-6 py-4">
				<div className="flex flex-1 flex-col justify-center">
					<h1 className="text-foreground text-2xl font-bold">Itinerarly</h1>
					<p className="text-muted-foreground">Creează și gestionează itinerariile tale</p>
				</div>

				<ModeToggle />
			</header>
			<main className="h-[calc(100vh-80px)]">
				<ItineraryCanvas />
			</main>
		</div>
	);
}
