import ItineraryCanvas from '~/components/itinerary-canvas';
import { ModeToggle } from '~/components/theme/theme-toggle-button';

export default function Home() {
	return (
		<div className="h-screen w-full">
			<header className="flex items-center justify-between border-b border-gray-200 bg-white px-6 py-4">
				<div className="flex flex-1 flex-col justify-center">
					<h1 className="text-2xl font-bold text-gray-900">Itinerarly</h1>
					<p className="text-gray-600">Creează și gestionează itinerariile tale</p>
				</div>

				<ModeToggle />
			</header>
			<main className="h-[calc(100vh-80px)]">
				<ItineraryCanvas />
			</main>
		</div>
	);
}
