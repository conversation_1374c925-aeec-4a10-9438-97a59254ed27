'use client';

import React, { useCallback, useMemo } from 'react';
import { ReactFlow, MiniMap, Controls, Background, useNodesState, useEdgesState, addEdge, Connection, Edge, BackgroundVariant } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import ItineraryNode from './nodes/itinerary-node';

// Tipurile de noduri disponibile
export type NodeType = 'start' | 'end' | 'transport' | 'accommodation' | 'activity' | 'restaurant';

// Interfața pentru datele unui nod
export interface ItineraryNodeData {
	label: string;
	type: NodeType;
	time?: string;
	day?: number;
	duration?: string;
	description?: string;
}

// Nodurile dummy pentru exemplu
const initialNodes = [
	{
		id: '1',
		type: 'itineraryNode',
		position: { x: 50, y: 100 },
		data: {
			label: 'Aeroport Otopeni',
			type: 'start' as NodeType,
			time: '08:00',
			day: 1,
			description: 'Punct de plecare - sosire în București'
		}
	},
	{
		id: '2',
		type: 'itineraryNode',
		position: { x: 300, y: 100 },
		data: {
			label: 'Metrou către centru',
			type: 'transport' as NodeType,
			time: '09:30',
			day: 1,
			duration: '45 min',
			description: 'Transport cu metroul M1 către Piața Victoriei'
		}
	},
	{
		id: '3',
		type: 'itineraryNode',
		position: { x: 400, y: 100 },
		data: {
			label: 'Hotel Central',
			type: 'accommodation' as NodeType,
			time: '10:30',
			day: 1,
			description: 'Check-in la hotel, depozitare bagaje'
		}
	},
	{
		id: '4',
		type: 'itineraryNode',
		position: { x: 700, y: 100 },
		data: {
			label: 'Centrul Vechi',
			type: 'activity' as NodeType,
			time: '11:00',
			day: 1,
			duration: '3 ore',
			description: 'Plimbare prin Centrul Vechi, vizitare Hanul lui Manuc'
		}
	},
	{
		id: '5',
		type: 'itineraryNode',
		position: { x: 900, y: 100 },
		data: {
			label: 'Restaurant Caru cu Bere',
			type: 'restaurant' as NodeType,
			time: '14:00',
			day: 1,
			duration: '1.5 ore',
			description: 'Prânz tradițional românesc'
		}
	},
	{
		id: '6',
		type: 'itineraryNode',
		position: { x: 300, y: 300 },
		data: {
			label: 'Palatul Parlamentului',
			type: 'activity' as NodeType,
			time: '10:00',
			day: 2,
			duration: '2 ore',
			description: 'Tur ghidat la Palatul Parlamentului'
		}
	},
	{
		id: '7',
		type: 'itineraryNode',
		position: { x: 500, y: 300 },
		data: {
			label: 'Parcul Herăstrău',
			type: 'activity' as NodeType,
			time: '13:00',
			day: 2,
			duration: '2 ore',
			description: 'Plimbare relaxantă în parc, vizita la Muzeul Satului'
		}
	},
	{
		id: '8',
		type: 'itineraryNode',
		position: { x: 700, y: 300 },
		data: {
			label: 'Aeroport Otopeni',
			type: 'end' as NodeType,
			time: '18:00',
			day: 2,
			description: 'Plecare - sfârșitul călătoriei'
		}
	}
];

// Conexiunile dintre noduri
const initialEdges = [
	{ id: 'e1-2', source: '1', target: '2', type: 'smoothstep' },
	{ id: 'e2-3', source: '2', target: '3', type: 'smoothstep' },
	{ id: 'e3-4', source: '3', target: '4', type: 'smoothstep' },
	{ id: 'e4-5', source: '4', target: '5', type: 'smoothstep' },
	{ id: 'e5-6', source: '5', target: '6', type: 'smoothstep' },
	{ id: 'e6-7', source: '6', target: '7', type: 'smoothstep' },
	{ id: 'e7-8', source: '7', target: '8', type: 'smoothstep' }
];

// Tipurile de noduri personalizate
const nodeTypes = {
	itineraryNode: ItineraryNode
};

export default function ItineraryCanvas() {
	const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
	const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

	const onConnect = useCallback((params: Connection) => setEdges((eds) => addEdge(params, eds)), [setEdges]);

	// Calculează statistici despre itinerariu
	const stats = useMemo(() => {
		const days = new Set(nodes.map((node) => node.data.day).filter(Boolean));
		const locations = nodes.length;
		return {
			totalDays: days.size,
			totalLocations: locations
		};
	}, [nodes]);

	return (
		<div className="relative h-full w-full">
			{/* Panel cu informații despre itinerariu */}
			<div className="absolute top-4 left-4 z-10 rounded-lg border bg-white p-4 shadow-lg">
				<h2 className="mb-2 text-lg font-bold">Itinerariu București</h2>
				<div className="flex gap-4 text-sm text-gray-600">
					<span>📅 {stats.totalDays} zile</span>
					<span>📍 {stats.totalLocations} locații</span>
				</div>
			</div>

			<ReactFlow
				nodes={nodes}
				edges={edges}
				onNodesChange={onNodesChange}
				onEdgesChange={onEdgesChange}
				onConnect={onConnect}
				nodeTypes={nodeTypes}
				fitView
				maxZoom={1.2}
				minZoom={0.8}
				className="bg-gray-50"
			>
				<Background variant={BackgroundVariant.Dots} gap={12} size={1} bgColor={'#f0f0f0'} />
			</ReactFlow>
		</div>
	);
}
