'use client';

import { addEdge, Background, BackgroundVariant, Connection, ReactFlow, useEdgesState, useNodesState } from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { useCallback, useMemo } from 'react';

import ItineraryNode from './nodes/itinerary-node';

// Tipurile de noduri disponibile
export type NodeType = 'start' | 'end' | 'transport' | 'accommodation' | 'activity' | 'restaurant';

// Interfața pentru datele unui nod
export interface ItineraryNodeData {
	label: string;
	type: NodeType;
	time?: string;
	day?: number;
	duration?: string;
	description?: string;
}

// Nodurile dummy pentru exemplu - organizate pe zile cu spațiere mai mare
const initialNodes = [
	// ZIUA 1 - Linia de sus (y: 150)
	{
		id: '1',
		type: 'itineraryNode',
		position: { x: 100, y: 150 },
		data: {
			label: 'Aeroport Otopeni',
			type: 'start' as NodeType,
			time: '08:00',
			day: 1,
			description: 'Punct de plecare - sosire în București'
		}
	},
	{
		id: '2',
		type: 'itineraryNode',
		position: { x: 400, y: 150 },
		data: {
			label: 'Metrou către centru',
			type: 'transport' as NodeType,
			time: '09:30',
			day: 1,
			duration: '45 min',
			description: 'Transport cu metroul M1 către Piața Victoriei'
		}
	},
	{
		id: '3',
		type: 'itineraryNode',
		position: { x: 700, y: 150 },
		data: {
			label: 'Hotel Central',
			type: 'accommodation' as NodeType,
			time: '10:30',
			day: 1,
			description: 'Check-in la hotel, depozitare bagaje'
		}
	},
	{
		id: '4',
		type: 'itineraryNode',
		position: { x: 1000, y: 150 },
		data: {
			label: 'Centrul Vechi',
			type: 'activity' as NodeType,
			time: '11:00',
			day: 1,
			duration: '3 ore',
			description: 'Plimbare prin Centrul Vechi, vizitare Hanul lui Manuc'
		}
	},
	{
		id: '5',
		type: 'itineraryNode',
		position: { x: 1300, y: 150 },
		data: {
			label: 'Restaurant Caru cu Bere',
			type: 'restaurant' as NodeType,
			time: '14:00',
			day: 1,
			duration: '1.5 ore',
			description: 'Prânz tradițional românesc'
		}
	},

	// ZIUA 2 - Linia de jos (y: 450)
	{
		id: '6',
		type: 'itineraryNode',
		position: { x: 400, y: 450 },
		data: {
			label: 'Palatul Parlamentului',
			type: 'activity' as NodeType,
			time: '10:00',
			day: 2,
			duration: '2 ore',
			description: 'Tur ghidat la Palatul Parlamentului'
		}
	},
	{
		id: '7',
		type: 'itineraryNode',
		position: { x: 700, y: 450 },
		data: {
			label: 'Parcul Herăstrău',
			type: 'activity' as NodeType,
			time: '13:00',
			day: 2,
			duration: '2 ore',
			description: 'Plimbare relaxantă în parc, vizita la Muzeul Satului'
		}
	},
	{
		id: '8',
		type: 'itineraryNode',
		position: { x: 1000, y: 450 },
		data: {
			label: 'Aeroport Otopeni',
			type: 'end' as NodeType,
			time: '18:00',
			day: 2,
			description: 'Plecare - sfârșitul călătoriei'
		}
	}
];

// Conexiunile dintre noduri
const initialEdges = [
	// Ziua 1 - conexiuni orizontale
	{ id: 'e1-2', source: '1', target: '2', type: 'smoothstep' },
	{ id: 'e2-3', source: '2', target: '3', type: 'smoothstep' },
	{ id: 'e3-4', source: '3', target: '4', type: 'smoothstep' },
	{ id: 'e4-5', source: '4', target: '5', type: 'smoothstep' },

	// Tranziția între zile - conexiune verticală
	{ id: 'e5-6', source: '5', target: '6', type: 'smoothstep', style: { stroke: '#10b981', strokeWidth: 3 } },

	// Ziua 2 - conexiuni orizontale
	{ id: 'e6-7', source: '6', target: '7', type: 'smoothstep' },
	{ id: 'e7-8', source: '7', target: '8', type: 'smoothstep' }
];

// Tipurile de noduri personalizate
const nodeTypes = {
	itineraryNode: ItineraryNode
};

export default function ItineraryCanvas() {
	const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
	const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

	const onConnect = useCallback((params: Connection) => setEdges((eds) => addEdge(params, eds)), [setEdges]);

	// Calculează statistici despre itinerariu
	const stats = useMemo(() => {
		const days = new Set(nodes.map((node) => node.data.day).filter(Boolean));
		const locations = nodes.length;
		return {
			totalDays: days.size,
			totalLocations: locations
		};
	}, [nodes]);

	return (
		<div className="bg-background relative h-full w-full">
			{/* Panel cu informații despre itinerariu */}
			<div className="border-border bg-card absolute top-4 left-4 z-10 rounded-lg border p-4 shadow-lg">
				<h2 className="text-card-foreground mb-2 text-lg font-bold">Itinerariu București</h2>
				<div className="text-muted-foreground flex gap-4 text-sm">
					<span>📅 {stats.totalDays} zile</span>
					<span>📍 {stats.totalLocations} locații</span>
				</div>
			</div>

			<ReactFlow
				nodes={nodes}
				edges={edges}
				onNodesChange={onNodesChange}
				onEdgesChange={onEdgesChange}
				onConnect={onConnect}
				nodeTypes={nodeTypes}
				fitView
				maxZoom={1}
				minZoom={0.6}
				defaultViewport={{ x: 0, y: 0, zoom: 0.8 }}
				className="bg-muted/30"
			>
				<Background variant={BackgroundVariant.Dots} gap={12} size={1} />
			</ReactFlow>
		</div>
	);
}
