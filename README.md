# Itinerarly

O aplicație modernă pentru crearea și managementul itinerariilor de călătorie, construită cu Next.js și React Flow.

## 🚀 Funcționalități

- **Canvas interactiv** cu noduri și conexiuni pentru vizualizarea itinerariilor
- **Tipuri diverse de noduri**: start, end, transport, cazare, activități, restaurante
- **Interfață intuitivă** similar cu n8n pentru editarea itinerariilor
- **Informații detaliate** pentru fiecare locație (timp, durată, descriere)
- **Organizare pe zile** pentru planificarea structurată
- **Design responsive** cu Tailwind CSS

## 🛠️ Stack Tehnologic

- **Frontend**: Next.js 15 + React 19
- **Canvas**: React Flow (@xyflow/react)
- **Styling**: Tailwind CSS 4
- **TypeScript**: Pentru type safety
- **Dezvoltare**: Turbopack pentru build rapid

## 📦 Instalare

```bash
# Clonează repository-ul
git clone <repository-url>
cd itinerarly

# Instalează dependențele
npm install

# Pornește serverul de dezvoltare
npm run dev
```

Deschide [http://localhost:3000](http://localhost:3000) în browser pentru a vedea aplicația.

## 🎯 Exemplu de Itinerariu

Aplicația vine cu un exemplu pre-configurat de itinerariu pentru București:

- **Ziua 1**: Aeroport → Metrou → Hotel → Centrul Vechi → Restaurant
- **Ziua 2**: Palatul Parlamentului → Parcul Herăstrău → Aeroport

## 🔧 Dezvoltare

```bash
# Dezvoltare
npm run dev

# Build pentru producție
npm run build

# Pornește aplicația în modul producție
npm start

# Linting
npm run lint
```

## 📁 Structura Proiectului

```
itinerarly/
├── app/
│   ├── page.tsx          # Pagina principală
│   ├── layout.tsx        # Layout-ul aplicației
│   └── globals.css       # Stiluri globale
├── components/
│   ├── ItineraryCanvas.tsx    # Canvas-ul principal
│   └── nodes/
│       └── ItineraryNode.tsx  # Componenta pentru noduri
└── public/               # Resurse statice
```

## 🎨 Tipuri de Noduri

- 🛫 **Start**: Punctul de plecare al călătoriei
- 🛬 **End**: Punctul final al călătoriei
- 🚇 **Transport**: Mijloace de transport (metrou, autobuz, etc.)
- 🏨 **Accommodation**: Cazare (hotel, pensiune, etc.)
- 🎯 **Activity**: Activități și atracții turistice
- 🍽️ **Restaurant**: Locuri de luat masa

## 🚧 Roadmap

- [ ] Autentificare cu better-auth
- [ ] Baza de date cu Prisma + PostgreSQL
- [ ] Editare avansată a nodurilor
- [ ] Export/import itinerarii
- [ ] Colaborare multi-utilizator
- [ ] Integrare cu API-uri de călătorie
- [ ] Animații cu Rive/Aceternity UI

## 📄 Licență

Acest proiect este licențiat sub MIT License.
