{"name": "itinerarly", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@xyflow/react": "^12.7.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.523.0", "next": "15.3.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}