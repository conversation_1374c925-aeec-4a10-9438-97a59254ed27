'use client';

import React from 'react';
import { Handle, Position, NodeProps } from '@xyflow/react';
import { ItineraryNodeData, NodeType } from '../itinerary-canvas';

// Iconuri pentru diferite tipuri de noduri
const getNodeIcon = (type: NodeType) => {
	switch (type) {
		case 'start':
			return '🛫';
		case 'end':
			return '🛬';
		case 'transport':
			return '🚇';
		case 'accommodation':
			return '🏨';
		case 'activity':
			return '🎯';
		case 'restaurant':
			return '🍽️';
		default:
			return '📍';
	}
};

// Culori pentru diferite tipuri de noduri
const getNodeColor = (type: NodeType) => {
	switch (type) {
		case 'start':
			return 'bg-green-100 border-green-400 text-green-800';
		case 'end':
			return 'bg-red-100 border-red-400 text-red-800';
		case 'transport':
			return 'bg-blue-100 border-blue-400 text-blue-800';
		case 'accommodation':
			return 'bg-purple-100 border-purple-400 text-purple-800';
		case 'activity':
			return 'bg-yellow-100 border-yellow-400 text-yellow-800';
		case 'restaurant':
			return 'bg-orange-100 border-orange-400 text-orange-800';
		default:
			return 'bg-gray-100 border-gray-400 text-gray-800';
	}
};

// Componenta pentru nodurile de itinerariu
export default function ItineraryNode({ data, selected }: NodeProps<ItineraryNodeData>) {
	const { label, type, time, day, duration, description } = data;
	const icon = getNodeIcon(type);
	const colorClass = getNodeColor(type);

	return (
		<div
			className={`relative max-w-[250px] min-w-[200px] rounded-lg border-2 p-4 shadow-lg transition-all duration-200 ${colorClass} ${
				selected ? 'scale-105 ring-2 ring-blue-400 ring-offset-2' : 'hover:scale-102'
			}`}
		>
			{/* Handle pentru conexiuni de intrare */}
			<Handle type="target" position={Position.Left} className="h-3 w-3 !bg-gray-400" />

			{/* Header cu icon și tip */}
			<div className="mb-2 flex items-center gap-2">
				<span className="text-lg">{icon}</span>
				<span className="text-xs font-semibold tracking-wide uppercase opacity-75">{type}</span>
			</div>

			{/* Titlul principal */}
			<h3 className="mb-2 text-sm leading-tight font-bold">{label}</h3>

			{/* Informații despre timp și zi */}
			<div className="mb-2 flex flex-wrap gap-2 text-xs">
				{day && <span className="bg-opacity-50 rounded bg-white px-2 py-1">Ziua {day}</span>}
				{time && <span className="bg-opacity-50 rounded bg-white px-2 py-1">{time}</span>}
				{duration && <span className="bg-opacity-50 rounded bg-white px-2 py-1">{duration}</span>}
			</div>

			{/* Descrierea */}
			{description && <p className="text-xs leading-relaxed opacity-75">{description}</p>}

			{/* Handle pentru conexiuni de ieșire */}
			<Handle type="source" position={Position.Right} className="h-3 w-3 !bg-gray-400" />
		</div>
	);
}
