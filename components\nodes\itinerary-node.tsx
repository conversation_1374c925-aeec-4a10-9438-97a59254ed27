'use client';

import React from 'react';
import { Handle, Position, NodeProps } from '@xyflow/react';
import { ItineraryNodeData, NodeType } from '../itinerary-canvas';

// Iconuri pentru diferite tipuri de noduri
const getNodeIcon = (type: NodeType) => {
	switch (type) {
		case 'start':
			return '🛫';
		case 'end':
			return '🛬';
		case 'transport':
			return '🚇';
		case 'accommodation':
			return '🏨';
		case 'activity':
			return '🎯';
		case 'restaurant':
			return '🍽️';
		default:
			return '📍';
	}
};

// Culori pentru diferite tipuri de noduri - theme aware
const getNodeColor = (type: NodeType) => {
	switch (type) {
		case 'start':
			return 'bg-green-50 border-green-200 text-green-800 dark:bg-green-950 dark:border-green-800 dark:text-green-200';
		case 'end':
			return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-950 dark:border-red-800 dark:text-red-200';
		case 'transport':
			return 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-950 dark:border-blue-800 dark:text-blue-200';
		case 'accommodation':
			return 'bg-purple-50 border-purple-200 text-purple-800 dark:bg-purple-950 dark:border-purple-800 dark:text-purple-200';
		case 'activity':
			return 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-950 dark:border-yellow-800 dark:text-yellow-200';
		case 'restaurant':
			return 'bg-orange-50 border-orange-200 text-orange-800 dark:bg-orange-950 dark:border-orange-800 dark:text-orange-200';
		default:
			return 'bg-muted border-border text-muted-foreground';
	}
};

// Componenta pentru nodurile de itinerariu
export default function ItineraryNode({ data, selected }: NodeProps<ItineraryNodeData>) {
	const { label, type, time, day, duration, description } = data;
	const icon = getNodeIcon(type);
	const colorClass = getNodeColor(type);

	return (
		<div
			className={`relative max-w-[250px] min-w-[200px] rounded-lg border-2 p-4 shadow-lg transition-all duration-200 ${colorClass} ${
				selected ? 'scale-105 ring-2 ring-blue-400 ring-offset-2' : 'hover:scale-102'
			}`}
		>
			{/* Handle pentru conexiuni de intrare */}
			<Handle type="target" position={Position.Left} className="!bg-muted-foreground !border-border h-3 w-3" />

			{/* Header cu icon și tip */}
			<div className="mb-2 flex items-center gap-2">
				<span className="text-lg">{icon}</span>
				<span className="text-xs font-semibold tracking-wide uppercase opacity-75">{type}</span>
			</div>

			{/* Titlul principal */}
			<h3 className="mb-2 text-sm leading-tight font-bold">{label}</h3>

			{/* Informații despre timp și zi */}
			<div className="mb-2 flex flex-wrap gap-2 text-xs">
				{day && <span className="bg-background/80 text-foreground rounded px-2 py-1">Ziua {day}</span>}
				{time && <span className="bg-background/80 text-foreground rounded px-2 py-1">{time}</span>}
				{duration && <span className="bg-background/80 text-foreground rounded px-2 py-1">{duration}</span>}
			</div>

			{/* Descrierea */}
			{description && <p className="text-xs leading-relaxed opacity-75">{description}</p>}

			{/* Handle pentru conexiuni de ieșire */}
			<Handle type="source" position={Position.Right} className="!bg-muted-foreground !border-border h-3 w-3" />
		</div>
	);
}
